'use client';
import { getTranslation } from '@/lib/i18n';
import { <PERSON>, CardHeader, CardBody, CardFooter } from '@heroui/react';

export default function Hero({ locale = 'en', color = 'black' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const data = {
    black: {
      title: t('Black Screen Tool'),
    },
    white: {
      title: t('White Screen Tool'),
    },
    red: {
      title: t('Red Screen Tool'),
    },
    green: {
      title: t('Green Screen Tool'),
    },
    blue: {
      title: t('Blue Screen Tool'),
    },
    yellow: {
      title: t('Yellow Screen Tool'),
    },
    orange: {
      title: t('Orange Screen Tool'),
    },
    pink: {
      title: t('Pink Screen Tool'),
    },
    purple: {
      title: t('Purple Screen Tool'),
    },
  };

  const { title } = data[color];

  return (
    <div className="text-center pt-10 pb-2">
      <h1 className="text-5xl font-bold text-primary mb-2">
        {title}
      </h1>
      <div className="grid lg:grid-cols-2 gap-8 my-12">
        <Card className="w-full md:w-2/5">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('Screen Preview')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div className='aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group'></div>
          </CardBody>
          <CardFooter></CardFooter>
        </Card>
        <Card className="w-full md:w-2/5">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('Screen Preview')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div className='aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group'></div>
          </CardBody>
          <CardFooter></CardFooter>
        </Card>
      </div>
      <div className="grid lg:grid-cols-2 gap-8 mb-12"></div>
    </div>
  );
}
